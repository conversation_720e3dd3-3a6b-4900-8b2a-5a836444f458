# EDDS Plugin - Weight and Dimensions Feature

## Overview
This feature adds automatic weight and dimension loading to the EDDS Ecommerce Dropshipping Suite plugin. Products imported from external platforms will now automatically have their weight and dimensions set in WooCommerce.

## Features Added

### 1. Settings Configuration
- **Import Product Weight**: Enable/disable automatic weight import
- **Import Product Dimensions**: Enable/disable automatic dimension import
- **Weight Unit**: Select from kg, g, lb, oz
- **Dimensions Unit**: Select from cm, mm, m, in, ft
- **Default Values**: Set default weight and dimensions for products without data

### 2. Import List Interface
- Weight field for each product
- Length, width, and height fields for each product
- Bulk editing capabilities for weight and dimensions

### 3. Automatic Import
- Weight and dimensions are automatically imported from source platform
- Falls back to default values if data is not available
- Supports unit conversion and validation

## Files Modified

### Core Files
1. `includes/data.php` - Added default parameters for weight/dimensions
2. `admin/api.php` - Updated data parsing to include weight/dimensions
3. `admin/import-list.php` - Added weight/dimension handling in import process
4. `admin/settings.php` - Added settings options for weight/dimensions

### Template Files
1. `templates/admin/html-import-list-item.php` - Added weight/dimension fields
2. `templates/admin/html-import-list-bulk-action-modal.php` - Added bulk editing
3. `templates/admin/html-import-list-header-section.php` - Added bulk actions

## Usage

### For Store Owners
1. Go to EDDS Settings
2. Configure weight and dimension import options
3. Set default values if needed
4. Import products as usual - weight and dimensions will be automatically loaded

### For Developers
The feature can be extended by:
- Adding custom weight/dimension validation
- Implementing unit conversion logic
- Adding custom bulk operations
- Integrating with shipping calculation plugins

## Technical Implementation

### Data Flow
1. Source platform provides weight/dimension data
2. API parses and stores the data
3. Import process applies settings and default values
4. WooCommerce product is created with weight/dimensions

### Settings Structure
```php
'import_weight' => 1,
'import_dimensions' => 1,
'weight_unit' => 'kg',
'dimensions_unit' => 'cm',
'default_weight' => '',
'default_length' => '',
'default_width' => '',
'default_height' => '',
```

### Form Fields
- Weight: `edds_product[product_id][weight]`
- Length: `edds_product[product_id][length]`
- Width: `edds_product[product_id][width]`
- Height: `edds_product[product_id][height]`

## Benefits
- Accurate shipping calculations
- Better inventory management
- Improved customer experience
- Automated data import
- Bulk editing capabilities

## Future Enhancements
- Unit conversion between different measurement systems
- Integration with shipping plugins
- Advanced validation rules
- Custom weight/dimension attributes 