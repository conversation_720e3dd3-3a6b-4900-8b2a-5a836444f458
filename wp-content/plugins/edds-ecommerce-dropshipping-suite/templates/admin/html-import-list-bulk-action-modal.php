<?php
defined( 'ABSPATH' ) || exit;
?>
    <div class="edds-modal-popup-container edds-hidden">
        <div class="edds-overlay"></div>
        <div class="edds-modal-popup-content edds-modal-popup-content-set-price">
            <div class="edds-modal-popup-header">
                <h2><?php esc_html_e( 'Set price', 'edds-ecommerce-dropshipping-suite' ) ?></h2>
                <span class="edds-modal-popup-close"> </span>
            </div>
            <div class="edds-modal-popup-content-body">
                <div class="edds-modal-popup-content-body-row">
                    <div class="edds-set-price-action-wrap">
                        <label for="edds-set-price-action"><?php esc_html_e( 'Action', 'edds-ecommerce-dropshipping-suite' ) ?></label>
                        <select id="edds-set-price-action"
                                class="edds-set-price-action">
                            <option value="set_new_value"><?php esc_html_e( 'Set to this value', 'edds-ecommerce-dropshipping-suite' ) ?></option>
                            <option value="increase_by_fixed_value">
								<?php esc_html_e( 'Increase by fixed value', 'edds-ecommerce-dropshipping-suite' );
								echo esc_html( '(' . get_woocommerce_currency_symbol() . ')' ) ?>
                            </option>
                            <option value="increase_by_percentage"><?php esc_html_e( 'Increase by percentage(%)', 'edds-ecommerce-dropshipping-suite' ) ?></option>
                        </select>
                    </div>
                    <div class="edds-set-price-amount-wrap">
                        <label for="edds-set-price-amount"><?php esc_html_e( 'Amount', 'edds-ecommerce-dropshipping-suite' ) ?></label>
                        <input type="text"
                               id="edds-set-price-amount"
                               class="edds-set-price-amount">
                    </div>
                </div>
            </div>
            <div class="edds-modal-popup-content-footer">
                        <span class="button button-primary edds-set-price-button-set">
                            <?php esc_html_e( 'Set', 'edds-ecommerce-dropshipping-suite' ) ?>
                        </span>
                <span class="button edds-bulk-action-button-cancel edds-set-price-button-cancel">
                            <?php esc_html_e( 'Cancel', 'edds-ecommerce-dropshipping-suite' ) ?>
                        </span>
            </div>
        </div>
        <div class="edds-modal-popup-content edds-modal-popup-content-remove-attribute">
            <div class="edds-modal-popup-header">
                <h2><?php esc_html_e( 'Please select default value to import after this attribute is removed', 'edds-ecommerce-dropshipping-suite' ) ?></h2>
                <span class="edds-modal-popup-close"> </span>
            </div>
            <div class="edds-modal-popup-content-body">
                <div class="edds-modal-popup-content-body-row edds-modal-popup-select-attribute">
                </div>
            </div>
        </div>
        <div class="edds-modal-popup-content edds-modal-popup-content-set-shipping_class">
			<div class="edds-modal-popup-content-header">
				<h2><?php esc_html_e( 'Bulk set product shipping class', 'edds-ecommerce-dropshipping-suite' ) ?></h2>
			</div>
			<div class="edds-modal-popup-content-body">
				<div class="edds-modal-popup-content-body-row edds-modal-popup-set-shipping_class">
					<div class="edds-modal-popup-set-shipping_class-select-wrap">
						<select name="bulk_set_shipping_class"
						        class="vi-ui dropdown fluid search edds-modal-popup-set-shipping_class-select">
							<option value=""><?php esc_html_e( 'No shipping class', 'edds-ecommerce-dropshipping-suite' ) ?></option>
							<?php
							if ( is_array( $shipping_class_options ) && !empty( $shipping_class_options ) ) {
								foreach ( $shipping_class_options as $shipping_class_id => $shipping_class_name ) {
									printf( "<option value='%s' >%s</option>", esc_attr( $shipping_class_id ), esc_html( $shipping_class_name ) );
								}
							}
							?>
						</select>
					</div>
					<div class="edds-modal-popup-content-body-row-button">
						<span class="button button-primary edds-set-shipping_class-button-set">
							<?php esc_html_e( 'Set', 'edds-ecommerce-dropshipping-suite' ) ?>
						</span>
						<span class="button vi-ui mini edds-bulk-action-button-cancel edds-set-shipping_class-button-cancel">
							<?php esc_html_e( 'Cancel', 'edds-ecommerce-dropshipping-suite' ) ?>
						</span>
					</div>
				</div>
			</div>
		</div>
		
		<div class="edds-modal-popup-content edds-modal-popup-content-set-weight">
			<div class="edds-modal-popup-content-header">
				<h2><?php esc_html_e( 'Bulk set product weight', 'edds-ecommerce-dropshipping-suite' ) ?></h2>
			</div>
			<div class="edds-modal-popup-content-body">
				<div class="edds-modal-popup-content-body-row edds-modal-popup-set-weight">
					<div class="edds-modal-popup-set-weight-input-wrap">
						<input type="number" step="0.01" min="0" 
						       class="vi-ui input edds-modal-popup-set-weight-input"
						       placeholder="<?php esc_attr_e( 'Enter weight value', 'edds-ecommerce-dropshipping-suite' ) ?>">
					</div>
					<div class="edds-modal-popup-content-body-row-button">
						<span class="button button-primary edds-set-weight-button-set">
							<?php esc_html_e( 'Set', 'edds-ecommerce-dropshipping-suite' ) ?>
						</span>
						<span class="button vi-ui mini edds-bulk-action-button-cancel edds-set-weight-button-cancel">
							<?php esc_html_e( 'Cancel', 'edds-ecommerce-dropshipping-suite' ) ?>
						</span>
					</div>
				</div>
			</div>
		</div>
		
		<div class="edds-modal-popup-content edds-modal-popup-content-set-dimensions">
			<div class="edds-modal-popup-content-header">
				<h2><?php esc_html_e( 'Bulk set product dimensions', 'edds-ecommerce-dropshipping-suite' ) ?></h2>
			</div>
			<div class="edds-modal-popup-content-body">
				<div class="edds-modal-popup-content-body-row edds-modal-popup-set-dimensions">
					<div class="equal width fields">
						<div class="field">
							<label><?php esc_html_e( 'Length', 'edds-ecommerce-dropshipping-suite' ) ?></label>
							<input type="number" step="0.01" min="0" 
							       class="vi-ui input edds-modal-popup-set-length-input"
							       placeholder="<?php esc_attr_e( 'Length', 'edds-ecommerce-dropshipping-suite' ) ?>">
						</div>
						<div class="field">
							<label><?php esc_html_e( 'Width', 'edds-ecommerce-dropshipping-suite' ) ?></label>
							<input type="number" step="0.01" min="0" 
							       class="vi-ui input edds-modal-popup-set-width-input"
							       placeholder="<?php esc_attr_e( 'Width', 'edds-ecommerce-dropshipping-suite' ) ?>">
						</div>
						<div class="field">
							<label><?php esc_html_e( 'Height', 'edds-ecommerce-dropshipping-suite' ) ?></label>
							<input type="number" step="0.01" min="0" 
							       class="vi-ui input edds-modal-popup-set-height-input"
							       placeholder="<?php esc_attr_e( 'Height', 'edds-ecommerce-dropshipping-suite' ) ?>">
						</div>
					</div>
					<div class="edds-modal-popup-content-body-row-button">
						<span class="button button-primary edds-set-dimensions-button-set">
							<?php esc_html_e( 'Set', 'edds-ecommerce-dropshipping-suite' ) ?>
						</span>
						<span class="button vi-ui mini edds-bulk-action-button-cancel edds-set-dimensions-button-cancel">
							<?php esc_html_e( 'Cancel', 'edds-ecommerce-dropshipping-suite' ) ?>
						</span>
					</div>
				</div>
			</div>
		</div>
        <div class="edds-modal-popup-content edds-modal-popup-content-set-categories">
            <div class="edds-modal-popup-header">
                <h2><?php esc_html_e( 'Bulk set product categories', 'edds-ecommerce-dropshipping-suite' ) ?></h2>
                <span class="edds-modal-popup-close"> </span>
            </div>
            <div class="edds-modal-popup-content-body">
                <div class="edds-modal-popup-content-body-row edds-modal-popup-set-categories">
                    <div class="edds-modal-popup-set-categories-select-wrap">
                        <select name="bulk_set_categories"
                                class="vi-ui dropdown fluid search edds-modal-popup-set-categories-select"
                                multiple>
							<?php
							if ( ! empty( $category_options ) ) {
								foreach ( $category_options as $cat_id => $cat_name ) {
									printf( "<option value='%s'>%s</option>", esc_attr( $cat_id ), esc_html( $cat_name ) );
								}
							}
							?>
                        </select>
                        <span class="vi-ui black button mini edds-modal-popup-set-categories-clear">
	                        <?php esc_html_e( 'Clear selected', 'edds-ecommerce-dropshipping-suite' ) ?>
                        </span>
                    </div>
                </div>
            </div>
            <div class="edds-modal-popup-content-footer">
                    <span class="button button-primary edds-set-categories-button-add"
                          title="<?php esc_attr_e( 'Add selected and keep existing categories', 'edds-ecommerce-dropshipping-suite' ) ?>">
	                    <?php esc_html_e( 'Add', 'edds-ecommerce-dropshipping-suite' ) ?>
                    </span>
                <span class="button button-primary edds-set-categories-button-set"
                      title="<?php esc_attr_e( 'Remove existing categories and add selected', 'edds-ecommerce-dropshipping-suite' ) ?>">
	                <?php esc_html_e( 'Set', 'edds-ecommerce-dropshipping-suite' ) ?>
                </span>
                <span class="button edds-bulk-action-button-cancel edds-set-categories-button-cancel">
	                <?php esc_html_e( 'Cancel', 'edds-ecommerce-dropshipping-suite' ) ?>
                </span>
            </div>
        </div>
        <div class="edds-modal-popup-content edds-modal-popup-content-set-tags">
            <div class="edds-modal-popup-header">
                <h2><?php esc_html_e( 'Bulk set product tags', 'edds-ecommerce-dropshipping-suite' ) ?></h2>
                <span class="edds-modal-popup-close"> </span>
            </div>
            <div class="edds-modal-popup-content-body">
                <div class="edds-modal-popup-content-body-row edds-modal-popup-set-tags">
                    <div class="edds-modal-popup-set-tags-select-wrap">
                        <select name="bulk_set_tags"
                                class="vi-ui dropdown fluid search edds-modal-popup-set-tags-select" multiple>
							<?php
							if ( ! empty( $tags_options ) ) {
								foreach ( $tags_options as $tag ) {
									printf( "<option value='%s'>%s</option>", esc_attr( $tag ), esc_html( $tag ) );
								}
							}
							?>
                        </select>
                        <span class="vi-ui black button mini edds-modal-popup-set-tags-clear">
	                        <?php esc_html_e( 'Clear selected', 'edds-ecommerce-dropshipping-suite' ) ?>
                        </span>
                    </div>
                </div>
            </div>
            <div class="edds-modal-popup-content-footer">
                    <span class="button button-primary edds-set-tags-button-add"
                          title="<?php esc_attr_e( 'Add selected and keep existing tags', 'edds-ecommerce-dropshipping-suite' ) ?>">
	                    <?php esc_html_e( 'Add', 'edds-ecommerce-dropshipping-suite' ) ?>
                    </span>
                <span class="button button-primary edds-set-tags-button-set"
                      title="<?php esc_attr_e( 'Remove existing tags and add selected', 'edds-ecommerce-dropshipping-suite' ) ?>">
	                <?php esc_html_e( 'Set', 'edds-ecommerce-dropshipping-suite' ) ?>
                </span>
                <span class="button edds-bulk-action-button-cancel edds-set-tags-button-cancel">
	                <?php esc_html_e( 'Cancel', 'edds-ecommerce-dropshipping-suite' ) ?>
                </span>
            </div>
        </div>
        <div class="edds-saving-overlay edds-hidden"></div>
    </div>
<?php