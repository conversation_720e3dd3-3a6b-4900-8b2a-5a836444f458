<?php
defined( 'ABSPATH' ) || exit;
$bulk_actions = [
	""                       => esc_html__( 'Bulk Action', 'edds-ecommerce-dropshipping-suite' ),
	"set_categories"         => esc_html__( 'Set categories', 'edds-ecommerce-dropshipping-suite' ),
	"set_tags"               => esc_html__( 'Set tags', 'edds-ecommerce-dropshipping-suite' ),
	"set_shipping_class"     => esc_html__( 'Set shipping class', 'edds-ecommerce-dropshipping-suite' ),
	"set_weight"             => esc_html__( 'Set weight', 'edds-ecommerce-dropshipping-suite' ),
	"set_dimensions"         => esc_html__( 'Set dimensions', 'edds-ecommerce-dropshipping-suite' ),
	"set_status_publish"     => esc_html__( 'Set status - Publish', 'edds-ecommerce-dropshipping-suite' ),
	"set_status_pending"     => esc_html__( 'Set status - Pending', 'edds-ecommerce-dropshipping-suite' ),
	"set_status_draft"       => esc_html__( 'Set status - Draft', 'edds-ecommerce-dropshipping-suite' ),
	"set_visibility_visible" => esc_html__( 'Set visibility - Shop and search results', 'edds-ecommerce-dropshipping-suite' ),
	"set_visibility_catalog" => esc_html__( 'Set visibility - Shop only', 'edds-ecommerce-dropshipping-suite' ),
	"set_visibility_search"  => esc_html__( 'Set visibility - Search results only', 'edds-ecommerce-dropshipping-suite' ),
	"set_visibility_hidden"  => esc_html__( 'Set visibility - Hidden', 'edds-ecommerce-dropshipping-suite' ),
	"import"                 => esc_html__( 'Import selected', 'edds-ecommerce-dropshipping-suite' ),
	"remove"                 => esc_html__( 'Remove selected', 'edds-ecommerce-dropshipping-suite' ),
];
?>
<form method="get" class="vi-ui segment edds-pagination-form">
    <input type="hidden" name="page" value="tmds">
	<?php do_action( 'edds_import_list_search_form' ); ?>
    <div class="tablenav top">
        <div class="edds-button-import-all-container">
            <input type="checkbox" class="edds-accordion-bulk-item-check-all">
            <span class="vi-ui button mini primary edds-button-import-all"
                  title="<?php esc_attr_e( 'Import all products on this page', 'edds-ecommerce-dropshipping-suite' ) ?>">
	            <?php esc_html_e( 'Import All', 'edds-ecommerce-dropshipping-suite' ) ?>
            </span>
            <a class="vi-ui button negative mini edds-button-empty-import-list"
               href="<?php echo esc_url( wp_nonce_url( add_query_arg( 'edds_empty_product_list', 1 ) ) ) ?>"
               title="<?php esc_attr_e( 'Remove all products(except overriding products) from Import list', 'edds-ecommerce-dropshipping-suite' ) ?>">
				<?php esc_html_e( 'Empty List', 'edds-ecommerce-dropshipping-suite' ) ?>
            </a>
            <span class="edds-accordion-bulk-actions-container">
                <select name="edds_bulk_actions"
                        class="vi-ui dropdown edds-accordion-bulk-actions">
                    <?php
                    foreach ( $bulk_actions as $value => $text ) {
	                    printf( "<option value='%s'>%s</option>", esc_attr( $value ), esc_html( $text ) );
                    }
                    ?>
                </select>
            </span>
        </div>
        <div class="tablenav-pages">
            <div class="pagination-links">
				<?php
				if ( $paged > 2 ) {
					?>
                    <a class="prev-page button" href="<?php echo esc_url( add_query_arg(
						array(
							'paged'            => 1,
							'edds_search' => $keyword,
						)
					) ) ?>">
                        <span class="screen-reader-text"><?php esc_html_e( 'First Page', 'edds-ecommerce-dropshipping-suite' ) ?></span>
                        <span aria-hidden="true">«</span>
                    </a>
					<?php
				} else {
					?>
                    <span class="tablenav-pages-navspan button disabled" aria-hidden="true">«</span>
					<?php
				}
				/*Previous button*/
				$p_paged = $per_page * $paged > $per_page ? $paged - 1 : 0;

				if ( $p_paged ) {
					$p_url = add_query_arg(
						array(
							'paged'            => $p_paged,
							'edds_search' => $keyword,
						)
					);
					?>
                    <a class="prev-page button" href="<?php echo esc_url( $p_url ) ?>">
                        <span class="screen-reader-text"><?php esc_html_e( 'Previous Page', 'edds-ecommerce-dropshipping-suite' ) ?></span>
                        <span aria-hidden="true">‹</span>
                    </a>
					<?php
				} else {
					?>
                    <span class="tablenav-pages-navspan button disabled" aria-hidden="true">‹</span>
					<?php
				}
				?>
                <span class="screen-reader-text"><?php esc_html_e( 'Current Page', 'edds-ecommerce-dropshipping-suite' ) ?></span>
                <span id="table-paging" class="paging-input">
                    <input class="current-page" type="text" name="paged" size="1"
                           value="<?php echo esc_html( $paged ) ?>">
                    <span class="tablenav-paging-text"> of <span
                                class="total-pages"><?php echo esc_html( $total_page ) ?></span></span>

                </span>
				<?php /*Next button*/
				$n_paged = $per_page * $paged < $count ? $paged + 1 : 0;
				if ( $n_paged ) {
					$n_url = add_query_arg(
						array(
							'paged'            => $n_paged,
							'edds_search' => $keyword,
						)
					); ?>
                    <a class="next-page button" href="<?php echo esc_url( $n_url ) ?>">
                        <span class="screen-reader-text"><?php esc_html_e( 'Next Page', 'edds-ecommerce-dropshipping-suite' ) ?></span>
                        <span aria-hidden="true">›</span>
                    </a>
					<?php
				} else {
					?>
                    <span class="tablenav-pages-navspan button disabled" aria-hidden="true">›</span>
					<?php
				}
				if ( $total_page > $paged + 1 ) {
					$next_page_url = add_query_arg( [
						'paged'            => $total_page,
						'edds_search' => $keyword,
					] );
					?>
                    <a class="next-page button" href="<?php echo esc_url( $next_page_url ) ?>">
                        <span class="screen-reader-text"><?php esc_html_e( 'Last Page', 'edds-ecommerce-dropshipping-suite' ) ?></span>
                        <span aria-hidden="true">»</span>
                    </a>
					<?php
				} else {
					?>
                    <span class="tablenav-pages-navspan button disabled" aria-hidden="true">»</span>
					<?php
				}
				?>
            </div>
        </div>
        <p class="search-box">
            <input type="search" class="text short" name="edds_search"
                   placeholder="<?php esc_attr_e( 'Search product in import list', 'edds-ecommerce-dropshipping-suite' ) ?>"
                   value="<?php echo esc_attr( $keyword ) ?>">
            <input type="submit" name="submit" class="button"
                   value="<?php echo esc_attr__( 'Search product', 'edds-ecommerce-dropshipping-suite' ) ?>">
        </p>
    </div>
</form>
